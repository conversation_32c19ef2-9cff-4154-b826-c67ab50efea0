<script lang="ts">
	import { onMount, afterUpdate, createEventDispatcher } from 'svelte';
	import { t } from '$lib/stores/i18n';

	import MessageItem from './MessageItem.svelte';
	import LoadingSpinner from '../common/LoadingSpinner.svelte';

	import type { Message } from '$lib/types/customer';
	import { formatMessageDate } from '$lib/utils/messageFormatter';

	import { TicketSolid, ArrowDownOutline } from 'flowbite-svelte-icons';

	// Configuration: Number of messages to load per request
	const MESSAGES_PER_LOAD = 1;

	export let platformId: number;
	export let messages: Message[] = [];
	export let loading: boolean = false;
	export let loadingMore: boolean = false;
	export let hasMore: boolean = true;
	export let focusedTicketId: number | null = null;
	export let useScrollAnimation: boolean = false;

	const dispatch = createEventDispatcher();

	let scrollContainer: HTMLElement;
	let shouldScrollToBottom = false; // Start as false to prevent unwanted auto-scrolling on init
	let isNearBottom = true;

	// Enhanced auto-scroll state
	let isInitialLoad = true;
	let previousMessageCount = 0;
	let previousPlatformId = platformId;
	let isLoadMoreOperation = false;

	// Sticky header state
	let stickyDate: string = '';
	let stickyTicketId: number | null = null;
	let showStickyHeader = true;
	let messageGroupElementsByTicket: HTMLElement[] = [];
	let dateGroupElements: HTMLElement[] = [];

	// Ticket navigation state
	let ticketElements: Map<number, HTMLElement> = new Map();
	let isNavigating = false;
	let scrollInProgress = false;

	// Scroll to bottom button state
	$: showScrollToBottomButton = !isNearBottom;

	onMount(() => {
		// Listen for ticket navigation events
		if (typeof window !== 'undefined') {
			window.addEventListener('navigate-to-ticket', handleNavigateToTicket as EventListener);
		}

		// Cleanup on destroy
		return () => {
			if (typeof window !== 'undefined') {
				window.removeEventListener('navigate-to-ticket', handleNavigateToTicket as EventListener);
			}
		};
	});

	// Context-aware scroll manager - system scrolling has priority
	async function performScroll(action: 'ticket-end' | 'bottom', ticketId?: number, forceInstant: boolean = false) {
		// If a scroll is already in progress, wait for it to complete
		if (scrollInProgress) {
			return;
		}

		scrollInProgress = true;

		try {
			if (action === 'ticket-end' && ticketId) {
				await scrollToTicketEnd(ticketId);
			} else if (action === 'bottom') {
				await executeBottomScroll(forceInstant);
				showScrollToBottomButton = false;
			}
		} finally {
			scrollInProgress = false;
		}
	}

	async function scrollToTicketEnd(ticketId: number): Promise<void> {
		const ticketElement = ticketElements.get(ticketId);
		
		if (ticketElement && scrollContainer) {
			isNavigating = true;
			shouldScrollToBottom = false;
			
			// Set focused ticket for visual highlighting
			focusedTicketId = ticketId;
			
			// Scroll to the end of the specific ticket with proper spacing
			const targetScrollTop = Math.max(0, 
				ticketElement.offsetTop + ticketElement.offsetHeight - scrollContainer.clientHeight + 100
			);
			
			scrollContainer.scrollTo({
				top: targetScrollTop,
				behavior: 'smooth'
			});
			
			// Wait for scroll animation to complete
			await new Promise(resolve => {
				setTimeout(() => {
					isNavigating = false;
					// Use enhanced bottom detection for consistency
					shouldScrollToBottom = isAtBottom(10);
					resolve(void 0);
				}, 1000);
			});
		}
	}

	// Wait for all images in the scroll container to load
	async function waitForImages(): Promise<void> {
		if (!scrollContainer) return;

		const images = scrollContainer.querySelectorAll('img');
		if (images.length === 0) return;

		const imagePromises = Array.from(images).map(img => {
			if (img.complete) return Promise.resolve();
			return new Promise<void>(resolve => {
				img.onload = () => resolve();
				img.onerror = () => resolve(); // Still resolve on error to not block
				// Timeout fallback in case image never loads
				setTimeout(() => resolve(), 2000);
			});
		});

		await Promise.all(imagePromises);
	}

	// Wait for all content to be fully rendered and sized
	async function waitForAllContent(): Promise<void> {
		if (!scrollContainer) return;

		// Wait for images to load first
		await waitForImages();

		// Wait for DOM updates to complete using multiple animation frames
		// This ensures all dynamic content has been rendered and sized
		await new Promise<void>(resolve => {
			requestAnimationFrame(() => {
				requestAnimationFrame(() => {
					requestAnimationFrame(() => {
						resolve();
					});
				});
			});
		});

		// Additional small delay to account for any remaining layout calculations
		await new Promise<void>(resolve => setTimeout(resolve, 50));
	}

	// Enhanced bottom detection with multiple verification methods
	function isAtBottom(tolerance: number = 10): boolean {
		if (!scrollContainer) return false;

		const { scrollTop, scrollHeight, clientHeight } = scrollContainer;

		// Method 1: Standard calculation
		const distanceFromBottom = scrollHeight - scrollTop - clientHeight;
		const method1 = distanceFromBottom <= tolerance;

		// Method 2: Direct position check
		const method2 = scrollTop >= (scrollHeight - clientHeight - tolerance);

		// Method 3: Percentage-based check (within 99.5% of total scroll)
		const scrollPercentage = scrollTop / (scrollHeight - clientHeight);
		const method3 = scrollPercentage >= 0.999;

		console.log(method1, method2, method3);

		// Return true if any method confirms we're at bottom
		return method1 || method2 || method3;
	}

	async function executeBottomScroll(forceInstant: boolean = false): Promise<void> {
		if (!scrollContainer) return;

		// Wait for all content to be fully rendered
		await waitForAllContent();

		return new Promise(resolve => {
			const attemptScroll = (attempt = 0) => {
				requestAnimationFrame(() => {
					try {
						const { scrollHeight, clientHeight } = scrollContainer;

						// Determine scroll behavior based on context and animation setting
						const scrollBehavior = forceInstant ? 'auto' : (useScrollAnimation ? 'smooth' : 'auto');

						const targetScrollTop = scrollHeight - clientHeight;
						scrollContainer.scrollTo({
							top: targetScrollTop,
							behavior: scrollBehavior as ScrollBehavior
						});

						// Verify we're actually at the bottom after a delay
						const verificationDelay = Math.min(100 + (attempt * 50), 300); // Increasing delay with attempts
						setTimeout(() => {
							if (isAtBottom(10)) {
								// Successfully at bottom
								resolve(void 0);
							} else if (attempt < 5) {
								// Retry up to 5 times with exponential backoff
								const retryDelay = Math.pow(2, attempt) * 25; // 25ms, 50ms, 100ms, 200ms, 400ms
								setTimeout(() => attemptScroll(attempt + 1), retryDelay);
							} else {
								// Final attempt: Force scroll to maximum possible position
								const maxScrollTop = Math.max(0, scrollHeight - clientHeight);
								scrollContainer.scrollTo({
									top: maxScrollTop + 50, // Add extra pixels to ensure we're past the bottom
									behavior: 'auto'
								});

								// Final verification after force scroll
								setTimeout(() => {
									resolve(void 0);
								}, 100);
							}
						}, verificationDelay);
					} catch (error) {
						// Log error but continue to retry
						// console.warn('Error attempting scroll:', error);
					}
				});
			};

			attemptScroll();
		});
	}

	afterUpdate(() => {
		const hasNewMessages = messages.length > previousMessageCount;
		const hasContextChanged = platformId !== previousPlatformId;

		if (hasContextChanged) {
			// Context change (platformId changed) - always use instant scroll regardless of animation setting
			if (messages.length > 0) {
				performScroll('bottom', undefined, true); // forceInstant = true
			}
			isInitialLoad = false;
			isLoadMoreOperation = false;
			previousPlatformId = platformId;
		} else if (isInitialLoad && messages.length > 0) {
			// Initial load - always use instant scroll
			performScroll('bottom', undefined, true); // forceInstant = true
			isInitialLoad = false;
		} else if (hasNewMessages && isNearBottom && !isNavigating && focusedTicketId === null && !isLoadMoreOperation) {
			// New messages arrived and user is at/near bottom - use animation setting
			performScroll('bottom');
		}

		// Reset load more operation flag after processing
		if (isLoadMoreOperation && hasNewMessages) {
			isLoadMoreOperation = false;
		}

		// Update message count for next comparison
		previousMessageCount = messages.length;

		// Update sticky header after DOM updates
		updateStickyHeader();
		// if(isNearBottom)
		// {
		// 	performScroll('bottom');
		// }
	});



	// Handle ticket selection from left panel - always scroll to ticket end
	function handleNavigateToTicket(event: Event) {
		const customEvent = event as CustomEvent;
		const { ticketId } = customEvent.detail;
		shouldScrollToBottom = false;

		// Always scroll to the end of the selected ticket
		performScroll('ticket-end', ticketId);
	}

	// Handle scroll to bottom button click
	function handleScrollToBottomClick() {
		performScroll('bottom');
	}



	function handleScroll(event: Event) {
		if (!scrollContainer) return;

		// Prevent manual scrolling when system is scrolling
		if (scrollInProgress) {
			event.preventDefault();
			return;
		}

		const { scrollTop, scrollHeight, clientHeight } = scrollContainer;
		const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

		// Check if user is near bottom (within 100px) - keep existing threshold for compatibility
		isNearBottom = distanceFromBottom < 100;

		// Check if scrolled to top for loading more
		if (scrollTop === 0 && messages.length > 0 && hasMore && !loadingMore) {
			isLoadMoreOperation = true;
			dispatch('loadMore', {
				limit: MESSAGES_PER_LOAD
			});
		}

		// Update sticky header based on scroll position
		updateStickyHeader();
	}

	function updateStickyHeader() {
		if (!scrollContainer || dateGroupElements.length === 0) return;

		const scrollTop = scrollContainer.scrollTop;
		const containerTop = scrollContainer.getBoundingClientRect().top;

		// Find the topmost visible date group
		let currentDateGroup = null;
		let currentTicketId = null;

		for (let i = 0; i < dateGroupElements.length; i++) {
			const element = dateGroupElements[i];
			if (!element) continue;

			const rect = element.getBoundingClientRect();
			const elementTop = rect.top - containerTop;

			// If this date group is visible or partially visible at the top
			if (elementTop <= 20) {
				// 20px threshold for sticky header activation
				const allDateGroups = getAllDateGroups();
				currentDateGroup = allDateGroups[i];
				// Find the ticket ID for this date group
				for (const ticketGroup of messageGroupedByTicketAndDate) {
					if (ticketGroup.dateGroups.includes(currentDateGroup)) {
						currentTicketId = ticketGroup.ticketId;
						break;
					}
				}
			} else {
				break;
			}
		}

		if (currentDateGroup && scrollTop > 50) {
			// Only show sticky header after scrolling 50px
			showStickyHeader = true;
			stickyDate = formatMessageDate(currentDateGroup.messages[0].created_on);
			stickyTicketId = currentTicketId;
		} else {
			showStickyHeader = false;
		}
	}

	// Helper function to get all date groups in a flat array for tracking
	function getAllDateGroups() {
		const allDateGroups: { date: string; messages: Message[] }[] = [];
		messageGroupedByTicketAndDate.forEach((ticketGroup) => {
			ticketGroup.dateGroups.forEach((dateGroup) => {
				allDateGroups.push(dateGroup);
			});
		});
		return allDateGroups;
	}

	// Helper function to calculate global date group index
	function getGlobalDateGroupIndex(ticketGroupIndex: number, dateGroupIndex: number): number {
		let globalIndex = 0;
		for (let i = 0; i < ticketGroupIndex; i++) {
			globalIndex += messageGroupedByTicketAndDate[i].dateGroups.length;
		}
		return globalIndex + dateGroupIndex;
	}

	// Group messages by ticket, then by date within each ticket
	function groupMessagesByTicketAndDate(messages: Message[]) {
		const groups: {
			ticketId: number;
			dateGroups: { date: string; messages: Message[] }[];
		}[] = [];
		let currentTicketId = -1;

		messages.forEach((msg) => {
			const msgDate = new Date(msg.created_on).toLocaleDateString();

			if (msg.ticket_id !== currentTicketId) {
				// New ticket group
				currentTicketId = msg.ticket_id;
				groups.push({
					ticketId: currentTicketId,
					dateGroups: [
						{
							date: msgDate,
							messages: [msg]
						}
					]
				});
				// For Debugging
				// console.log('MessageList.svelte: groupMessagesByTicketAndDate(): New ticket group created:', groups[groups.length - 1]);
			} else {
				// Same ticket, check if we need a new date group
				const currentTicketGroup = groups[groups.length - 1];
				const lastDateGroup =
					currentTicketGroup.dateGroups[currentTicketGroup.dateGroups.length - 1];

				if (lastDateGroup.date !== msgDate) {
					// New date within the same ticket
					currentTicketGroup.dateGroups.push({
						date: msgDate,
						messages: [msg]
					});
				} else {
					// Same date, add to existing date group
					lastDateGroup.messages.push(msg);
				}
			}
		});
		return groups;
	}

	// Check if should show avatar (first message or different sender)
	function shouldShowAvatar(message: Message, index: number, messages: Message[]) {
		if (index === 0) return true;
		const prevMessage = messages[index - 1];
		return prevMessage.is_self !== message.is_self || prevMessage.user_name !== message.user_name;
	}

	$: messageGroupedByTicketAndDate = groupMessagesByTicketAndDate(messages);

	// Calculate total number of date groups across all tickets
	$: totalDateGroups = messageGroupedByTicketAndDate.reduce((total, ticketGroup) => {
		return total + ticketGroup.dateGroups.length;
	}, 0);

	// Ensure messageGroupElementsByTicket array is properly sized for the new nested structure
	$: if (messageGroupedByTicketAndDate.length !== messageGroupElementsByTicket.length) {
		messageGroupElementsByTicket = new Array(messageGroupedByTicketAndDate.length);
	}

	// Ensure dateGroupElements array is properly sized
	$: if (totalDateGroups !== dateGroupElements.length) {
		dateGroupElements = new Array(totalDateGroups);
	}

	// Update ticketElements map when messageGroupElementsByTicket changes
	$: if (messageGroupElementsByTicket && messageGroupedByTicketAndDate.length > 0) {
		ticketElements.clear();
		messageGroupedByTicketAndDate.forEach((ticketGroup, index) => {
			if (messageGroupElementsByTicket[index]) {
				ticketElements.set(ticketGroup.ticketId, messageGroupElementsByTicket[index]);
			}
		});
	}
</script>

<div class="message-list-wrapper relative flex-1 flex flex-col">
	<div
		id="message-list-container"
		bind:this={scrollContainer}
		on:scroll={handleScroll}
		class="custom-scrollbar flex-1 overflow-y-auto bg-gray-50 px-6 py-4 {scrollInProgress ? 'disable-scroll' : ''} {useScrollAnimation ? 'smooth-scroll' : 'instant-scroll'}"
	>
	<!-- Sticky Header -->
	{#if showStickyHeader}
		<div id="message-list-sticky-header" class="sticky top-0 z-10 mb-4 pb-2 transition-all duration-200">
			<div class="my-4 flex items-center justify-center">
				<span
					id="message-list-sticky-date"
					class="flex items-center justify-center rounded-full bg-gray-900 bg-opacity-40 px-3 py-1 text-center text-xs text-white"
				>
					{#if stickyTicketId}
						<TicketSolid class="mr-2 h-4 w-4" />
						#{stickyTicketId} • 
					{/if}
					{stickyDate}
				</span>
			</div>
		</div>
	{/if}
	{#if loading && messages.length === 0}
		<div id="message-list-initial-loading" class="flex h-full items-center justify-center">
			<LoadingSpinner />
		</div>
	{:else}
		<!-- Load more indicator at top -->
		{#if loadingMore}
			<div id="message-list-load-more-indicator" class="flex items-center justify-center py-3 text-gray-500">
				<LoadingSpinner size="sm" />
				<span class="ml-2 text-sm">{t('chat_center_loading_more_messages')}</span>
			</div>
		{/if}

		<!-- Messages grouped by ticket and date -->
		{#each messageGroupedByTicketAndDate as ticketGroup, ticketGroupIndex}
			{#if ticketGroup.ticketId}
				<div 
					bind:this={messageGroupElementsByTicket[ticketGroupIndex]} 
					class="message-group {focusedTicketId === ticketGroup.ticketId ? 'focused-ticket' : ''}"
					id="message-list-ticket-group-{ticketGroup.ticketId}" 
				>
					<!-- Ticket separator -->
					<div id="message-list-ticket-separator-{ticketGroup.ticketId}" class="my-10 my-4 flex items-center justify-center">
						<span
							class="flex items-center justify-center rounded-full transition-all duration-500
								{focusedTicketId === ticketGroup.ticketId 
									? 'bg-blue-600 px-4 py-2 text-sm shadow-lg border-2 border-blue-300' 
									: 'bg-gray-900 bg-opacity-40 px-3 py-1 text-xs'} 
								text-white"
							id="message-list-ticket-badge-{ticketGroup.ticketId}"	   
						>
							<TicketSolid class="mr-2 h-5 w-5" />
							{ticketGroup.ticketId}
						</span>
					</div>

					<!-- Date groups within ticket -->
					{#each ticketGroup.dateGroups as dateGroup, dateGroupIndex}
						{@const globalDateGroupIndex = getGlobalDateGroupIndex(ticketGroupIndex, dateGroupIndex)}
						<!-- Date separator -->
						<div
							id="message-list-date-separator-{dateGroup.date.replace(/\//g, '-')}"
							bind:this={dateGroupElements[globalDateGroupIndex]}
							class="my-4 flex items-center justify-center"
						>
							<span
								id="message-list-date-badge-{dateGroup.date.replace(/\//g, '-')}"
								class="flex items-center justify-center rounded-full bg-gray-900 bg-opacity-40 px-3 py-1 text-center text-xs text-white"
							>
								{formatMessageDate(dateGroup.messages[0].created_on)}
							</span>
						</div>

						<!-- Messages in date group -->
						<div id="message-list-messages-{dateGroup.date.replace(/\//g, '-')}">
							{#each dateGroup.messages as message, messageIndex}
								<MessageItem
									{message}
									showAvatar={shouldShowAvatar(message, messageIndex, dateGroup.messages)}
								/>
							{/each}
						</div>
					{/each}
				</div>
			{/if}
		{/each}

		{#if messages.length === 0}
			<div id="message-list-empty-state" class="mt-8 text-center text-gray-500">
				{t('no_messages')}
			</div>
		{/if}
	{/if}
	</div>

	<!-- Floating Scroll to Bottom Button -->
	{#if showScrollToBottomButton}
		<button
			id="message-list-scroll-to-bottom-button"
			class="scroll-to-bottom-button z-20 flex h-8 w-8 items-center justify-center rounded bg-gray-900 bg-opacity-80 text-white shadow-lg transition-all duration-300 hover:bg-opacity-100 hover:shadow-xl focus:outline-none"
			on:click={handleScrollToBottomClick}
			aria-label={t('chat_center_scroll_to_bottom')}
			title={t('chat_center_scroll_to_bottom')}
		>
			<ArrowDownOutline class="h-6 w-6" />
		</button>
	{/if}
</div>

<style>
	/* Dynamic scroll behavior classes */
	.smooth-scroll {
		scroll-behavior: smooth;
	}

	.instant-scroll {
		scroll-behavior: auto;
	}

	/* Disable scrolling when system is scrolling */
	.disable-scroll {
		pointer-events: none;
		user-select: none;
	}

	/* Focused ticket styling */
	.focused-ticket {
		border-left: 4px solid #3b82f6;
		padding-left: 1.5rem;
		margin-left: -1.5rem;
		border-radius: 0.5rem;
	}

	/* Scroll to bottom button positioning and animations */
	.scroll-to-bottom-button {
		animation: fadeInUp 0.3s ease-out;
		/* Position relative to the wrapper container's visible area */
		position: absolute;
		bottom: 1rem;
		right: 1rem;
		/* Ensure it stays in place during scroll */
		transform: translateZ(0);
		will-change: transform;
	}

	/* Ensure the wrapper can contain absolutely positioned elements and handles flex layout */
	.message-list-wrapper {
		position: relative;
		min-height: 0; /* Allow flex child to shrink below content size */
	}

	@keyframes fadeInUp {
		from {
			opacity: 0;
			transform: translateY(10px) translateZ(0);
		}
		to {
			opacity: 1;
			transform: translateY(0) translateZ(0);
		}
	}
</style>
