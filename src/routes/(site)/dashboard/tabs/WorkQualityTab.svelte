<script lang="ts">
    import BarChart from '$lib/components/dashboard/BarChart.svelte';
    import LineChart from '$lib/components/dashboard/LineChart.svelte';
    import ScoreCard from '$lib/components/dashboard/ScoreCard.svelte';
    import { COLORS } from '$lib/components/dashboard/colors';
    import { t, dict } from '$lib/stores/i18n';
    import thLocaleData from '$lib/locales/th.json';
    import { onMount } from 'svelte';
    import { currentLanguage, languagePreference } from '$lib/stores/languagePreference';

    // Define API Base URL
    import { getBackendUrl } from '$src/lib/config';

    // Define props for startDate and endDate
    export let startDate: string | undefined;
    export let endDate: string | undefined;

    // Expanded state variables for modals
    let isAgentChatbotExpanded: boolean = false;
    let isDailyCsatExpanded: boolean = false;
    let isDailyFirstResponseTimeExpanded: boolean = false;
    let isDailyResponseTimeExpanded: boolean = false;
    let isOverallSentimentExpanded: boolean = false;
    let isDailySentimentExpanded: boolean = false;
    let isCaseTypeSentimentExpanded: boolean = false;

    // Loading and error states for API connections - these will still hold backend messages for internal logging
    let isLoadingAgentChatbot: boolean = true;
    let agentChatbotError: string | null = null;
    let isLoadingSentimentSummary: boolean = true;
    let sentimentSummaryError: string | null = null;
    let isLoadingDailySentiment: boolean = true;
    let dailySentimentError: string | null = null;
    let isLoadingCaseTypeSentiment: boolean = true;
    let caseTypeSentimentError: string | null = null;

    // Loading and error states for the CSAT Score and Line Chart
    let isLoadingCSAT: boolean = true;
    let csatError: string | null = null;
    let isLoadingAvgFirstResponseTime: boolean = true;
    let avgFirstResponseTimeError: string | null = null;
    let isLoadingAvgResponseTime: boolean = true;
    let avgResponseTimeError: string | null = null;

    // Reactive variable to determine if the current language is Thai
    $: isThaiLocale = $dict === thLocaleData;

    // Define interfaces for API responses (remain the same)
    interface ScoreAndTimeSeriesAPIResponse {
        main_period: {
            start_date: string;
            end_date: string;
            metric_value: number | null;
            time_series_data: { date: string; value: number | null }[];
        };
        comparison_period: {
            start_date: string;
            end_date: string;
            metric_value: number | null;
            time_series_data: { date: string; value: number | null }[];
        };
        percentage_change: number | null;
        units: string;
    }

    interface ResponderResponseTimeAPIResponse {
        responder_type: 'agent' | 'chatbot';
        total_count: number;
        raw_avg: number;
    }

    interface SentimentAnalysisSummaryAPIResponse {
        time: string;
        positive: number;
        neutral: number;
        negative: number;
    }

    interface SentimentTimeSeriesAPIResponse {
        time: string;
        positive: number;
        neutral: number;
        negative: number;
    }

    interface SentimentByCaseTypeAPIResponse {
        case_type: string;
        positive: number;
        neutral: number;
        negative: number;
    }

    // Define interfaces for the formatted data for charts (remain the same)
    interface AgentChatbotComparisonDataItem {
        type: string;
        count: number;
    }

    interface OverallSentimentAmountItem {
        label: 'Positive' | 'Neutral' | 'Negative';
        value: number;
    }

    interface DailySentimentTimelineItem {
        label: string;
        positive: number;
        neutral: number;
        negative: number;
    }

    interface ProductSentimentAmountItem {
        product: string;
        positive: number;
        neutral: number;
        negative: number;
    }

    interface LineChartDataItem {
        label: string;
        value: number;
    }

    // Data fetched from backend for charts
    let agentChatbotComparisonData: AgentChatbotComparisonDataItem[] = [];
    let overallSentimentAmounts: OverallSentimentAmountItem[] = [];
    let overallSentimentColors: string[] = [];
    let dailySentimentTimeline: DailySentimentTimelineItem[] = [];
    let productSentimentAmounts: ProductSentimentAmountItem[] = [];

    // Data state variables for Scorecard and Line Charts
    // These are now union types to accept string 'No data available'
    let averageCSAT: number | string | null = null;
    let csatPercentageChange: number | null = null;
    let avgCSAT: LineChartDataItem[] = [];
    let averageFirstResponseTimeSeconds: number | string | null = null;
    let firstResponseTimePercentageChange: number | null = null;
    let avgFirstResponseTime: LineChartDataItem[] = [];
    let averageResponseTimeSeconds: number | string | null = null;
    let averageResponseTimePercentageChange: number | null = null;
    let avgResponseTime: LineChartDataItem[] = [];

    function formatDateForChart(dateString: string): string {
        const date = new Date(dateString);
        return date.toLocaleDateString(
            isThaiLocale ? 'th-TH-u-ca-buddhist' : 'en-US',
            { month: 'short', day: 'numeric'}
        );
    }

    async function fetchData() {
        console.log("fetchData called for Work Quality tab");
        console.log(`Current filter settings (from props): startDate='${startDate}', endDate='${endDate}'`);

        // Build query params using URLSearchParams
        const urlParams = new URLSearchParams();
        if (startDate) urlParams.append('start_date', startDate);
        if (endDate) urlParams.append('end_date', endDate);

        // Only add lang=th if language is Thai
        const currentLang = languagePreference.getCurrentLanguage();
        if (currentLang === 'th') {
            urlParams.append('lang', 'th');
        }

        const queryString = urlParams.toString();
        const baseUrl = getBackendUrl();

        // Define all fetch promises
        const endpoints = [
            'csat-score-time-series',
            'first-response-time',
            'average-response-time',
            'responder-response-time',
            'sentiment-analysis-summary',
            'sentiment-analysis-time-series',
            'sentiment-analysis-by-case-type'
        ];

        const requests = endpoints.map(endpoint =>
            fetch(`${baseUrl}/dashboard/api/${endpoint}/?${queryString}`)
        );

        // Reset all loading and error states at the beginning
        isLoadingCSAT = true;
        csatError = null;
        isLoadingAvgFirstResponseTime = true;
        avgFirstResponseTimeError = null;
        isLoadingAvgResponseTime = true;
        avgResponseTimeError = null;
        isLoadingAgentChatbot = true;
        agentChatbotError = null;
        isLoadingSentimentSummary = true;
        sentimentSummaryError = null;
        isLoadingDailySentiment = true;
        dailySentimentError = null;
        isLoadingCaseTypeSentiment = true;
        caseTypeSentimentError = null;

        // Reset all data to empty/null before fetching
        agentChatbotComparisonData = [];
        overallSentimentAmounts = [];
        overallSentimentColors = [];
        dailySentimentTimeline = [];
        productSentimentAmounts = [];
        averageCSAT = null;
        csatPercentageChange = null;
        avgCSAT = [];
        averageFirstResponseTimeSeconds = null;
        firstResponseTimePercentageChange = null;
        avgFirstResponseTime = [];
        averageResponseTimeSeconds = null;
        averageResponseTimePercentageChange = null;
        avgResponseTime = [];

        const results = await Promise.allSettled(requests);

        // Helper function to process successful responses with generics for type safety
        const processResponse = async <T>(response: PromiseSettledResult<Response>, dataProcessor: (data: T) => void) => {
            if (response.status === 'fulfilled' && response.value.ok) {
                const data: T = await response.value.json();
                dataProcessor(data);
            } else {
                const error = response.status === 'rejected' ? response.reason.message : `HTTP error! status: ${response.value.status}`;
                throw new Error(error);
            }
        };

        // Process CSAT Score and Daily CSAT (Time Series)
        try {
            await processResponse<ScoreAndTimeSeriesAPIResponse>(results[0], (csatData) => {
                console.log("Fetched CSAT Score data:", csatData);
                if (csatData && csatData.main_period) {
                    averageCSAT = csatData.main_period.metric_value !== null ? parseFloat(csatData.main_period.metric_value.toFixed(1)) : null;
                    csatPercentageChange = csatData.percentage_change !== null ? parseFloat(csatData.percentage_change.toFixed(1)) : null;
                    avgCSAT = csatData.main_period.time_series_data.map(item => ({
                        label: formatDateForChart(item.date),
                        value: item.value !== null ? parseFloat(item.value.toFixed(1)) : 0
                    }));
                } else {
                    csatError = 'No data available from backend for CSAT Score.';
                    averageCSAT = t('db.noDataAvailable');
                }
            });
        } catch (error: any) {
            console.error('Error fetching CSAT data:', error);
            csatError = `Failed to load CSAT data: ${error.message}`;
            averageCSAT = t('db.noDataAvailable');
            avgCSAT = [];
        } finally {
            isLoadingCSAT = false;
        }

        // Process Average First Response Time (seconds)
        try {
            await processResponse<ScoreAndTimeSeriesAPIResponse>(results[1], (firstResponseTimeData) => {
                console.log("Fetched First Response Time data:", firstResponseTimeData);
                if (firstResponseTimeData && firstResponseTimeData.main_period) {
                    averageFirstResponseTimeSeconds = firstResponseTimeData.main_period.metric_value !== null ? parseFloat(firstResponseTimeData.main_period.metric_value.toFixed(1)) : null;
                    firstResponseTimePercentageChange = firstResponseTimeData.percentage_change !== null ? parseFloat(firstResponseTimeData.percentage_change.toFixed(1)) : null;
                    avgFirstResponseTime = firstResponseTimeData.main_period.time_series_data.map(item => ({
                        label: formatDateForChart(item.date),
                        value: item.value !== null ? parseFloat(item.value.toFixed(1)) : 0
                    }));
                } else {
                    avgFirstResponseTimeError = 'No data available from backend for Average First Response Time.';
                    averageFirstResponseTimeSeconds = t('db.noDataAvailable');
                }
            });
        } catch (error: any) {
            console.error('Error fetching Average First Response Time data:', error);
            avgFirstResponseTimeError = `Failed to load Average First Response Time data: ${error.message}`;
            averageFirstResponseTimeSeconds = t('db.noDataAvailable');
            avgFirstResponseTime = [];
        } finally {
            isLoadingAvgFirstResponseTime = false;
        }

        // Process Average Response Time (seconds) Daily
        try {
            await processResponse<ScoreAndTimeSeriesAPIResponse>(results[2], (averageResponseTimeData) => {
                console.log("Fetched Average Response Time data:", averageResponseTimeData);
                if (averageResponseTimeData && averageResponseTimeData.main_period) {
                    averageResponseTimeSeconds = averageResponseTimeData.main_period.metric_value !== null ? parseFloat(averageResponseTimeData.main_period.metric_value.toFixed(1)) : null;
                    averageResponseTimePercentageChange = averageResponseTimeData.percentage_change !== null ? parseFloat(averageResponseTimeData.percentage_change.toFixed(1)) : null;
                    avgResponseTime = averageResponseTimeData.main_period.time_series_data.map(item => ({
                        label: formatDateForChart(item.date),
                        value: item.value !== null ? parseFloat(item.value.toFixed(1)) : 0
                    }));
                } else {
                    avgResponseTimeError = 'No data available from backend for Average Response Time.';
                    averageResponseTimeSeconds = t('db.noDataAvailable');
                }
            });
        } catch (error: any) {
            console.error('Error fetching Average Response Time data:', error);
            avgResponseTimeError = `Failed to load Average Response Time data: ${error.message}`;
            averageResponseTimeSeconds = t('db.noDataAvailable');
            avgResponseTime = [];
        } finally {
            isLoadingAvgResponseTime = false;
        }

        // Process Average Response Time (seconds): Agent vs. Chatbot
        try {
            await processResponse<ResponderResponseTimeAPIResponse[]>(results[3], (responderResponseTimeData) => {
                console.log("Fetched Agent vs. Chatbot data:", responderResponseTimeData);
                if (Array.isArray(responderResponseTimeData) && responderResponseTimeData.length > 0) {
                    agentChatbotComparisonData = responderResponseTimeData.map(item => ({
                        type: item.responder_type === 'agent' ? 'Agent' : 'Chatbot',
                        count: parseFloat((item.raw_avg ?? 0).toFixed(1))
                    }));
                } else {
                    agentChatbotError = 'No data available from backend for Agent vs. Chatbot comparison.';
                }
            });
        } catch (error: any) {
            console.error('Error fetching Agent vs. Chatbot data:', error);
            agentChatbotError = `Failed to load Agent vs. Chatbot data: ${error.message}`;
            agentChatbotComparisonData = [];
        } finally {
            isLoadingAgentChatbot = false;
        }

        // Process Total Sentiment Count
        try {
            await processResponse<SentimentAnalysisSummaryAPIResponse[]>(results[4], (sentimentSummaryData) => {
                console.log("Fetched Total Sentiment Count data:", sentimentSummaryData);
                let totalPositive = 0;
                let totalNeutral = 0;
                let totalNegative = 0;
                if (Array.isArray(sentimentSummaryData) && sentimentSummaryData.length > 0) {
                    sentimentSummaryData.forEach(item => {
                        totalPositive += item.positive;
                        totalNeutral += item.neutral;
                        totalNegative += item.negative;
                    });
                } else {
                    sentimentSummaryError = 'No data available from backend for Total Sentiment Count.';
                }
                overallSentimentAmounts = [
                    { label: 'Positive', value: totalPositive },
                    { label: 'Neutral', value: totalNeutral },
                    { label: 'Negative', value: totalNegative },
                ];
                overallSentimentColors = overallSentimentAmounts.map(item => {
                    if (item.label === 'Positive') return COLORS.green;
                    if (item.label === 'Neutral') return COLORS.silver;
                    if (item.label === 'Negative') return COLORS.red;
                    return COLORS.lightGray; // Fallback
                });
            });
        } catch (error: any) {
            console.error('Error fetching Total Sentiment Count data:', error);
            sentimentSummaryError = `Failed to load Total Sentiment Count data: ${error.message}`;
            overallSentimentAmounts = [];
            overallSentimentColors = [];
        } finally {
            isLoadingSentimentSummary = false;
        }

        // Process Daily Sentiment Count (Time Series)
        try {
            await processResponse<SentimentTimeSeriesAPIResponse[]>(results[5], (sentimentTimeSeriesData) => {
                console.log("Fetched Daily Sentiment Count data:", sentimentTimeSeriesData);
                if (Array.isArray(sentimentTimeSeriesData) && sentimentTimeSeriesData.length > 0) {
                    dailySentimentTimeline = sentimentTimeSeriesData.map(item => ({
                        label: formatDateForChart(item.time),
                        positive: item.positive,
                        neutral: item.neutral,
                        negative: item.negative,
                    }));
                } else {
                    dailySentimentError = 'No data available from backend for Daily Sentiment Count.';
                }
            });
        } catch (error: any) {
            console.error('Error fetching Daily Sentiment Count data:', error);
            dailySentimentError = `Failed to load Daily Sentiment Count data: ${error.message}`;
            dailySentimentTimeline = [];
        } finally {
            isLoadingDailySentiment = false;
        }

        // Process Sentiment Count by Case Type
        try {
            await processResponse<SentimentByCaseTypeAPIResponse[]>(results[6], (sentimentByCaseTypeData) => {
                console.log("Fetched Sentiment by Case Type data:", sentimentByCaseTypeData);
                if (Array.isArray(sentimentByCaseTypeData) && sentimentByCaseTypeData.length > 0) {
                    productSentimentAmounts = sentimentByCaseTypeData.map(item => ({
                        product: item.case_type,
                        positive: item.positive,
                        neutral: item.neutral,
                        negative: item.negative,
                    }));
                } else {
                    caseTypeSentimentError = 'No data available from backend for Sentiment by Case Type.';
                }
            });
        } catch (error: any) {
            console.error('Error fetching Sentiment by Case Type data:', error);
            caseTypeSentimentError = `Failed to load Sentiment by Case Type data: ${error.message}`;
            productSentimentAmounts = [];
        } finally {
            isLoadingCaseTypeSentiment = false;
        }
    }

    // Functions to add for downloading Excel files
    async function downloadAverageCsatScoreDailyExcel() {
        console.log("Attempting to download daily average csat score excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = new URLSearchParams();

        if (startDate) urlParams.append('start_date', startDate);
        if (endDate) urlParams.append('end_date', endDate);

        // Append language param only if Thai
        if (currentLang === 'th') {
            urlParams.append('lang', 'th');
        }

        const query = urlParams.toString();
        const downloadUrl = `${getBackendUrl()}/dashboard/api/csat-score-time-series.xlsx?${query}`;
        console.log("Download URL:", downloadUrl);

        try {
            const response = await fetch(downloadUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let filename;

            // Use backend filename only if current language is Thai
            if (currentLang === 'th') {
                const contentDisposition = response.headers.get('Content-Disposition');
                const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            // Fallback to frontend translation-based filename
            filename ??= `${t('dbWorkQuality.averageCsatScoreOutOf5DailyExcel')}_${startDate}_${endDate}.xlsx`;

            // Create blob and trigger download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(t('db.excelAlert'));
        }
    }    

    async function downloadAverageFirstResponseTimeSecondsDailyExcel() {
        console.log("Attempting to download daily average first response time excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = new URLSearchParams();

        if (startDate) urlParams.append('start_date', startDate);
        if (endDate) urlParams.append('end_date', endDate);

        // Append language param only if Thai
        if (currentLang === 'th') {
            urlParams.append('lang', 'th');
        }

        const query = urlParams.toString();
        const downloadUrl = `${getBackendUrl()}/dashboard/api/first-response-time.xlsx?${query}`;
        console.log("Download URL:", downloadUrl);

        try {
            const response = await fetch(downloadUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let filename;

            // Use backend filename only if current language is Thai
            if (currentLang === 'th') {
                const contentDisposition = response.headers.get('Content-Disposition');
                const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            // Fallback to frontend translation-based filename
            filename ??= `${t('dbWorkQuality.averageFirstResponseTimeSecondsDailyExcel')}_${startDate}_${endDate}.xlsx`;

            // Create blob and trigger download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(t('db.excelAlert'));
        }
    }        

    async function downloadAverageResponseTimeSecondsDailyExcel() {
        console.log("Attempting to download daily average response time excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = new URLSearchParams();

        if (startDate) urlParams.append('start_date', startDate);
        if (endDate) urlParams.append('end_date', endDate);

        // Append language param only if Thai
        if (currentLang === 'th') {
            urlParams.append('lang', 'th');
        }

        const query = urlParams.toString();
        const downloadUrl = `${getBackendUrl()}/dashboard/api/average-response-time.xlsx?${query}`;
        console.log("Download URL:", downloadUrl);

        try {
            const response = await fetch(downloadUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let filename;

            // Use backend filename only if current language is Thai
            if (currentLang === 'th') {
                const contentDisposition = response.headers.get('Content-Disposition');
                const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            // Fallback to frontend translation-based filename
            filename ??= `${t('dbWorkQuality.averageResponseTimeSecondsDailyExcel')}_${startDate}_${endDate}.xlsx`;

            // Create blob and trigger download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(t('db.excelAlert'));
        }
    }        

    async function downloadAverageResponseTimeSecondsAgentVsChatbotExcel() {
        console.log("Attempting to download daily average response time agent vs. chatbot excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = new URLSearchParams();

        if (startDate) urlParams.append('start_date', startDate);
        if (endDate) urlParams.append('end_date', endDate);

        // Append language param only if Thai
        if (currentLang === 'th') {
            urlParams.append('lang', 'th');
        }

        const query = urlParams.toString();
        const downloadUrl = `${getBackendUrl()}/dashboard/api/responder-response-time.xlsx?${query}`;
        console.log("Download URL:", downloadUrl);

        try {
            const response = await fetch(downloadUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let filename;

            // Use backend filename only if current language is Thai
            if (currentLang === 'th') {
                const contentDisposition = response.headers.get('Content-Disposition');
                const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            // Fallback to frontend translation-based filename
            filename ??= `${t('dbWorkQuality.averageResponseTimeSecondsAgentVsChatbotExcel')}_${startDate}_${endDate}.xlsx`;

            // Create blob and trigger download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(t('db.excelAlert'));
        }
    }        

    async function downloadtotalSentimentCountExcel() {
        console.log("Attempting to download total sentiment count excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = new URLSearchParams();

        if (startDate) urlParams.append('start_date', startDate);
        if (endDate) urlParams.append('end_date', endDate);

        // Append language param only if Thai
        if (currentLang === 'th') {
            urlParams.append('lang', 'th');
        }

        const query = urlParams.toString();
        const downloadUrl = `${getBackendUrl()}/dashboard/api/sentiment-analysis-summary.xlsx?${query}`;
        console.log("Download URL:", downloadUrl);

        try {
            const response = await fetch(downloadUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let filename;

            // Use backend filename only if current language is Thai
            if (currentLang === 'th') {
                const contentDisposition = response.headers.get('Content-Disposition');
                const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            // Fallback to frontend translation-based filename
            filename ??= `${t('dbWorkQuality.totalSentimentCountExcel')}_${startDate}_${endDate}.xlsx`;

            // Create blob and trigger download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(t('db.excelAlert'));
        }
    }            

    async function downloaddailySentimentCountExcel() {
        console.log("Attempting to download daily sentiment count excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = new URLSearchParams();

        if (startDate) urlParams.append('start_date', startDate);
        if (endDate) urlParams.append('end_date', endDate);

        // Append language param only if Thai
        if (currentLang === 'th') {
            urlParams.append('lang', 'th');
        }

        const query = urlParams.toString();
        const downloadUrl = `${getBackendUrl()}/dashboard/api/sentiment-analysis-time-series.xlsx?${query}`;
        console.log("Download URL:", downloadUrl);

        try {
            const response = await fetch(downloadUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let filename;

            // Use backend filename only if current language is Thai
            if (currentLang === 'th') {
                const contentDisposition = response.headers.get('Content-Disposition');
                const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            // Fallback to frontend translation-based filename
            filename ??= `${t('dbWorkQuality.dailySentimentCountExcel')}_${startDate}_${endDate}.xlsx`;

            // Create blob and trigger download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(t('db.excelAlert'));
        }
    }          

    async function downloadsentimentCountClosedTicketsByCaseTypeExcel() {
        console.log("Attempting to download sentiment count closed tickets by case type excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = new URLSearchParams();

        if (startDate) urlParams.append('start_date', startDate);
        if (endDate) urlParams.append('end_date', endDate);

        // Append language param only if Thai
        if (currentLang === 'th') {
            urlParams.append('lang', 'th');
        }

        const query = urlParams.toString();
        const downloadUrl = `${getBackendUrl()}/dashboard/api/sentiment-analysis-by-case-type.xlsx?${query}`;
        console.log("Download URL:", downloadUrl);

        try {
            const response = await fetch(downloadUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let filename;

            // Use backend filename only if current language is Thai
            if (currentLang === 'th') {
                const contentDisposition = response.headers.get('Content-Disposition');
                const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            // Fallback to frontend translation-based filename
            filename ??= `${t('dbWorkQuality.sentimentCountClosedTicketsByCaseTypeExcel')}_${startDate}_${endDate}.xlsx`;

            // Create blob and trigger download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(t('db.excelAlert'));
        }
    }              

    onMount(() => {
        console.log("WorkQualityTab component mounted: initiating fetchData");
        fetchData();
    });

    // Re-run fetchData whenever these reactive variables change, including isThaiLocale
    $: startDate, endDate, isThaiLocale, fetchData();
</script>

{#if isAgentChatbotExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbWorkQuality.averageResponseTimeSecondsAgentVsChatbot')}</h3>
                <button on:click={() => isAgentChatbotExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full h-full flex items-center justify-center">
                {#if isLoadingAgentChatbot}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if agentChatbotError || agentChatbotComparisonData.length === 0}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <BarChart
                        data={agentChatbotComparisonData}
                        label=""
                        barColor={COLORS.blue}
                        chartType="verticalBar"
                        labelKey="type"
                        valueKey="count"
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isDailyCsatExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbWorkQuality.averageCsatScoreOutOf5Daily')}</h3>
                <button on:click={() => isDailyCsatExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full h-full flex items-center justify-center">
                {#if isLoadingCSAT}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if csatError || avgCSAT.length === 0}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <LineChart
                        data={avgCSAT}
                        chartLabel={t('dbWorkQuality.averageCsat')}
                        lineColor={COLORS.blue}
                        showDataLabels={false}
                        showLegend={false}
                        chartHeight="100%"
                    />
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isDailyFirstResponseTimeExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbWorkQuality.averageFirstResponseTimeSecondsDaily')}</h3>
                <button on:click={() => isDailyFirstResponseTimeExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full h-full flex items-center justify-center">
                {#if isLoadingAvgFirstResponseTime}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if avgFirstResponseTimeError || avgFirstResponseTime.length === 0}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <LineChart
                        data={avgFirstResponseTime}
                        chartLabel={t('dbWorkQuality.timeSeconds')}
                        lineColor={COLORS.blue}
                        showDataLabels={false}
                        showLegend={false}
                        chartHeight="100%"
                    />
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isDailyResponseTimeExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbWorkQuality.averageResponseTimeSecondsDaily')}</h3>
                <button on:click={() => isDailyResponseTimeExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full h-full flex items-center justify-center">
                {#if isLoadingAvgResponseTime}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if avgResponseTimeError || avgResponseTime.length === 0}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <LineChart
                        data={avgResponseTime}
                        chartLabel={t('dbWorkQuality.timeSeconds')}
                        lineColor={COLORS.blue}
                        showDataLabels={false}
                        showLegend={false}
                        chartHeight="100%"
                    />
                {/if}
            </div>
        </div>
    </div>
{/if}


{#if isOverallSentimentExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbWorkQuality.totalSentimentCount')}</h3>
                <button on:click={() => isOverallSentimentExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full h-full flex items-center justify-center">
                {#if isLoadingSentimentSummary}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if sentimentSummaryError || overallSentimentAmounts.length === 0 || overallSentimentAmounts.every(s => s.value === 0)}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <BarChart
                        data={overallSentimentAmounts}
                        label={t('dbWorkQuality.ticketCount')}
                        barColor={overallSentimentColors}
                        borderColor={overallSentimentColors}
                        chartType="verticalBar"
                        labelKey="label"
                        valueKey="value"
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isDailySentimentExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbWorkQuality.dailySentimentCount')}</h3>
                <button on:click={() => isDailySentimentExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full h-full flex items-center justify-center">
                {#if isLoadingDailySentiment}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if dailySentimentError || dailySentimentTimeline.length === 0}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <BarChart
                        data={dailySentimentTimeline}
                        label={t('dbWorkQuality.ticketCount')}
                        chartType="stackedVerticalBar"
                        labelKey="label"
                        stackedKeys={['positive', 'neutral', 'negative']}
                        stackedColors={{
                            positive: COLORS.green,
                            neutral: COLORS.silver,
                            negative: COLORS.red
                        }}
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isCaseTypeSentimentExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbWorkQuality.sentimentCountClosedTicketsByCaseType')}</h3>
                <button on:click={() => isCaseTypeSentimentExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full h-full flex items-center justify-center">
                {#if isLoadingCaseTypeSentiment}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if caseTypeSentimentError || productSentimentAmounts.length === 0 || productSentimentAmounts.every(p => p.positive === 0 && p.neutral === 0 && p.negative === 0)}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <BarChart
                        data={productSentimentAmounts} label={t('dbWorkQuality.ticketCount')}
                        chartType="stackedHorizontalBar"
                        labelKey="product"
                        stackedKeys={['positive', 'neutral', 'negative']}
                        stackedColors={{
                            positive: COLORS.green,
                            neutral: COLORS.silver,
                            negative: COLORS.red
                        }}
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>
    </div>
{/if}

<div class="flex flex-col gap-2">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
        <div class="grid grid-cols-1 gap-2">
            <div class="bg-white rounded-lg shadow-md p-6 flex flex-col justify-between">
                <ScoreCard
                    title={t('dbWorkQuality.averageCsatScoreOutOf5')}
                    value={averageCSAT}
                    valueColor="text-black-600"
                    trendValue={csatPercentageChange}
                    trendUnit="%"
                />
            </div>
            <div class="bg-white rounded-lg shadow-md p-6 flex flex-col justify-between">
                <ScoreCard
                    title={t('dbWorkQuality.averageFirstResponseTimeSeconds')}
                    value={averageFirstResponseTimeSeconds}
                    valueColor="text-black-600"
                    trendValue={firstResponseTimePercentageChange}
                    trendUnit="%"
                    isTrendPositiveIsGood={false}
                />
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbWorkQuality.averageResponseTimeSecondsAgentVsChatbot')}</h2>
                <div class="flex gap-2 flex-wrap sm:flex-nowrap">
                    <button 
                        on:click={() => isAgentChatbotExpanded = true} 
                        class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap">
                        {t('db.expand')}
                    </button>
                    <button 
                        on:click={downloadAverageResponseTimeSecondsAgentVsChatbotExcel}
                        class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap">
                        {t('db.downloadExcel')}
                    </button>
                </div>
            </div>
            <div class="w-full h-[14rem] flex items-center justify-center">
                {#if isLoadingAgentChatbot}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if agentChatbotError || agentChatbotComparisonData.length === 0}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <BarChart
                        data={agentChatbotComparisonData}
                        label=""
                        barColor={COLORS.blue}
                        chartType="verticalBar"
                        labelKey="type"
                        valueKey="count"
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>
    </div>
    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-2">
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbWorkQuality.averageCsatScoreOutOf5Daily')}</h2>
                <div class="flex gap-2 flex-wrap sm:flex-nowrap">
                    <button
                        on:click={() => isDailyCsatExpanded = true}
                        class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap">
                        {t('db.expand')}
                    </button>
                    <button
                        on:click={downloadAverageCsatScoreDailyExcel}
                        class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap">
                        {t('db.downloadExcel')}
                    </button>
                </div>
            </div>
            <div class="w-full h-70 flex items-center justify-center"> {#if isLoadingCSAT}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if csatError || avgCSAT.length === 0}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <LineChart
                        data={avgCSAT}
                        chartLabel={t('dbWorkQuality.averageCsat')}
                        lineColor={COLORS.blue}
                        showDataLabels={false}
                        showLegend={false}
                        chartHeight="20rem"
                    />
                {/if}
            </div>
        </div>
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbWorkQuality.averageFirstResponseTimeSecondsDaily')}</h2>
                <div class="flex gap-2 flex-wrap sm:flex-nowrap">
                    <button
                        on:click={() => isDailyFirstResponseTimeExpanded = true}
                        class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap">
                        {t('db.expand')}
                    </button>
                    <button
                        on:click={downloadAverageFirstResponseTimeSecondsDailyExcel}
                        class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap">
                        {t('db.downloadExcel')}
                    </button>
                </div>
            </div>
            <div class="w-full h-70 flex items-center justify-center"> {#if isLoadingAvgFirstResponseTime}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if avgFirstResponseTimeError || avgFirstResponseTime.length === 0}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <LineChart
                        data={avgFirstResponseTime}
                        chartLabel={t('dbWorkQuality.timeSeconds')}
                        lineColor={COLORS.blue}
                        showDataLabels={false}
                        showLegend={false}
                        chartHeight="20rem"
                    />
                {/if}
            </div>
        </div>
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbWorkQuality.averageResponseTimeSecondsDaily')}</h2>
                <div class="flex gap-2 flex-wrap sm:flex-nowrap">
                    <button
                        on:click={() => isDailyResponseTimeExpanded = true}
                        class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap">
                        {t('db.expand')}
                    </button>
                    <button
                        on:click={downloadAverageResponseTimeSecondsDailyExcel}
                        class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap">
                        {t('db.downloadExcel')}
                    </button>
                </div>
            </div>
            <div class="w-full h-70 flex items-center justify-center"> {#if isLoadingAvgResponseTime}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if avgResponseTimeError || avgResponseTime.length === 0}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <LineChart
                        data={avgResponseTime}
                        chartLabel={t('dbWorkQuality.timeSeconds')}
                        lineColor={COLORS.blue}
                        showDataLabels={false}
                        showLegend={false}
                        chartHeight="20rem"
                    />
                {/if}
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-2">
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbWorkQuality.totalSentimentCount')}</h2>
                <div class="flex gap-2 flex-wrap sm:flex-nowrap">
                    <button 
                        on:click={() => isOverallSentimentExpanded = true}
                        class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap">
                        {t('db.expand')}
                    </button>
                    <button 
                        on:click={downloadtotalSentimentCountExcel}
                        class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap">
                        {t('db.downloadExcel')}
                    </button>
                </div>
            </div>
            <div class="w-full h-72 flex items-center justify-center">
                {#if isLoadingSentimentSummary}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if sentimentSummaryError || overallSentimentAmounts.length === 0 || overallSentimentAmounts.every(s => s.value === 0)}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <BarChart
                        data={overallSentimentAmounts}
                        label={t('dbWorkQuality.ticketCount')}
                        barColor={overallSentimentColors}
                        borderColor={overallSentimentColors}
                        chartType="verticalBar"
                        labelKey="label"
                        valueKey="value"
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbWorkQuality.dailySentimentCount')}</h2>
                <div class="flex gap-2 flex-wrap sm:flex-nowrap">
                    <button 
                        on:click={() => isDailySentimentExpanded = true}
                        class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap">
                        {t('db.expand')}
                    </button>
                    <button 
                        on:click={downloaddailySentimentCountExcel}
                        class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap">
                        {t('db.downloadExcel')}
                    </button>
                </div>
            </div>
            <div class="w-full h-72 flex items-center justify-center">
                {#if isLoadingDailySentiment}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if dailySentimentError || dailySentimentTimeline.length === 0}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <BarChart
                        data={dailySentimentTimeline}
                        label={t('dbWorkQuality.ticketCount')}
                        chartType="stackedVerticalBar"
                        labelKey="label"
                        stackedKeys={['positive', 'neutral', 'negative']}
                        stackedColors={{
                            positive: COLORS.green,
                            neutral: COLORS.silver,
                            negative: COLORS.red
                        }}
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbWorkQuality.sentimentCountClosedTicketsByCaseType')}</h2>
                <div class="flex gap-2 flex-wrap sm:flex-nowrap">
                    <button 
                        on:click={() => isCaseTypeSentimentExpanded = true}
                        class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap">
                        {t('db.expand')}
                    </button>
                    <button 
                        on:click={downloadsentimentCountClosedTicketsByCaseTypeExcel}
                        class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap">
                        {t('db.downloadExcel')}
                    </button>
                </div>
            </div>
            <div class="w-full h-72 flex items-center justify-center">
                {#if isLoadingCaseTypeSentiment}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if caseTypeSentimentError || productSentimentAmounts.length === 0 || productSentimentAmounts.every(p => p.positive === 0 && p.neutral === 0 && p.negative === 0)}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <BarChart
                        data={productSentimentAmounts} label={t('dbWorkQuality.ticketCount')}
                        chartType="stackedHorizontalBar"
                        labelKey="product"
                        stackedKeys={['positive', 'neutral', 'negative']}
                        stackedColors={{
                            positive: COLORS.green,
                            neutral: COLORS.silver,
                            negative: COLORS.red
                        }}
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>
    </div>
</div>